<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\DeviceToken;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;

class FCMIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $deviceToken;
    protected $authToken;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create();
        
        // Create test device token
        $this->deviceToken = DeviceToken::create([
            'token' => 'test_token_' . uniqid(),
            'device_type' => 'android',
            'user_id' => $this->user->id,
            'is_active' => true
        ]);

        // Get auth token
        $response = $this->postJson('/api/auth/login', [
            'email' => $this->user->email,
            'password' => 'password'
        ]);
        
        $this->authToken = $response->json('token');
    }

    public function test_send_test_notification()
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->authToken
        ])->postJson('/api/fcm/test', [
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true
            ]);

        // Check if notification was logged
        Log::shouldReceive('info')
            ->with('FCM notification sent successfully', Mockery::any())
            ->once();
    }

    public function test_send_notification_with_platform_configs()
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->authToken
        ])->postJson('/api/fcm/send', [
            'token' => $this->deviceToken->token,
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'high',
            'category' => 'test',
            'android' => [
                'priority' => 'high',
                'notification' => [
                    'channel_id' => 'high_importance_channel',
                    'sound' => 'default'
                ]
            ],
            'apns' => [
                'headers' => [
                    'apns-priority' => '10'
                ],
                'payload' => [
                    'aps' => [
                        'sound' => 'default',
                        'badge' => 1
                    ]
                ]
            ]
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true
            ]);
    }

    public function test_send_bulk_notifications()
    {
        // Create multiple device tokens
        $tokens = [];
        for ($i = 0; $i < 3; $i++) {
            $tokens[] = DeviceToken::create([
                'token' => 'test_token_' . uniqid(),
                'device_type' => 'android',
                'user_id' => $this->user->id,
                'is_active' => true
            ])->token;
        }

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->authToken
        ])->postJson('/api/fcm/bulk', [
            'tokens' => $tokens,
            'notification' => [
                'title' => 'Bulk Test Notification',
                'message' => 'This is a bulk test notification',
                'severity' => 'normal',
                'category' => 'test'
            ]
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true
            ])
            ->assertJsonStructure([
                'data' => [
                    'success_count',
                    'failure_count'
                ]
            ]);
    }

    public function test_rate_limiting()
    {
        $maxAttempts = config('fcm.rate_limits.test_notifications', 10);

        // Send notifications up to the rate limit
        for ($i = 0; $i < $maxAttempts; $i++) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->authToken
            ])->postJson('/api/fcm/test', [
                'title' => 'Test Notification ' . $i,
                'message' => 'This is test notification ' . $i,
                'severity' => 'normal',
                'category' => 'test'
            ]);

            $response->assertStatus(200);
        }

        // Try to send one more notification
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->authToken
        ])->postJson('/api/fcm/test', [
            'title' => 'Rate Limited Notification',
            'message' => 'This should be rate limited',
            'severity' => 'normal',
            'category' => 'test'
        ]);

        $response->assertStatus(429)
            ->assertJson([
                'success' => false,
                'message' => 'Too many requests. Please try again later.'
            ]);
    }

    public function test_invalid_token_handling()
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->authToken
        ])->postJson('/api/fcm/send', [
            'token' => 'invalid_token',
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false
            ]);
    }
} 