<?php

namespace Tests\Unit\Exceptions;

use Tests\TestCase;
use App\Exceptions\FCMException;
use Kreait\Firebase\Exception\MessagingException;

class FCMExceptionTest extends TestCase
{
    public function test_exception_creation()
    {
        $exception = new FCMException('Test error message');

        $this->assertInstanceOf(FCMException::class, $exception);
        $this->assertEquals('Test error message', $exception->getMessage());
        $this->assertEquals(0, $exception->getCode());
    }

    public function test_exception_creation_with_code()
    {
        $exception = new FCMException('Test error message', 500);

        $this->assertInstanceOf(FCMException::class, $exception);
        $this->assertEquals('Test error message', $exception->getMessage());
        $this->assertEquals(500, $exception->getCode());
    }

    public function test_exception_creation_with_previous_exception()
    {
        $previousException = new MessagingException('Previous error');
        $exception = new FCMException('Test error message', 0, $previousException);

        $this->assertInstanceOf(FCMException::class, $exception);
        $this->assertEquals('Test error message', $exception->getMessage());
        $this->assertEquals(0, $exception->getCode());
        $this->assertSame($previousException, $exception->getPrevious());
    }

    public function test_exception_creation_with_context()
    {
        $context = [
            'token' => 'test_token',
            'notification_id' => 1
        ];

        $exception = new FCMException('Test error message', 0, null, $context);

        $this->assertInstanceOf(FCMException::class, $exception);
        $this->assertEquals('Test error message', $exception->getMessage());
        $this->assertEquals(0, $exception->getCode());
        $this->assertEquals($context, $exception->getContext());
    }

    public function test_exception_creation_with_all_parameters()
    {
        $previousException = new MessagingException('Previous error');
        $context = [
            'token' => 'test_token',
            'notification_id' => 1
        ];

        $exception = new FCMException('Test error message', 500, $previousException, $context);

        $this->assertInstanceOf(FCMException::class, $exception);
        $this->assertEquals('Test error message', $exception->getMessage());
        $this->assertEquals(500, $exception->getCode());
        $this->assertSame($previousException, $exception->getPrevious());
        $this->assertEquals($context, $exception->getContext());
    }

    public function test_exception_to_array()
    {
        $context = [
            'token' => 'test_token',
            'notification_id' => 1
        ];

        $exception = new FCMException('Test error message', 500, null, $context);

        $expected = [
            'message' => 'Test error message',
            'code' => 500,
            'context' => $context
        ];

        $this->assertEquals($expected, $exception->toArray());
    }

    public function test_exception_to_array_with_previous_exception()
    {
        $previousException = new MessagingException('Previous error');
        $context = [
            'token' => 'test_token',
            'notification_id' => 1
        ];

        $exception = new FCMException('Test error message', 500, $previousException, $context);

        $expected = [
            'message' => 'Test error message',
            'code' => 500,
            'context' => $context,
            'previous' => [
                'message' => 'Previous error',
                'code' => 0
            ]
        ];

        $this->assertEquals($expected, $exception->toArray());
    }

    public function test_exception_to_json()
    {
        $context = [
            'token' => 'test_token',
            'notification_id' => 1
        ];

        $exception = new FCMException('Test error message', 500, null, $context);

        $expected = json_encode([
            'message' => 'Test error message',
            'code' => 500,
            'context' => $context
        ]);

        $this->assertEquals($expected, $exception->toJson());
    }

    public function test_exception_to_string()
    {
        $context = [
            'token' => 'test_token',
            'notification_id' => 1
        ];

        $exception = new FCMException('Test error message', 500, null, $context);

        $expected = "FCMException: Test error message (500)\nContext: " . json_encode($context);

        $this->assertEquals($expected, (string) $exception);
    }

    public function test_exception_to_string_with_previous_exception()
    {
        $previousException = new MessagingException('Previous error');
        $context = [
            'token' => 'test_token',
            'notification_id' => 1
        ];

        $exception = new FCMException('Test error message', 500, $previousException, $context);

        $expected = "FCMException: Test error message (500)\nContext: " . json_encode($context) . "\nPrevious: Previous error";

        $this->assertEquals($expected, (string) $exception);
    }
} 