.test-container {
  max-width: 600px;
  margin: 0 auto;
}

.notification-item {
  margin-bottom: 8px;
  border-radius: 8px;
  --background: var(--ion-color-light);
}

.category-badge {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

ion-badge {
  font-size: 0.7rem;
  padding: 4px 8px;
}

ion-card {
  margin-bottom: 16px;
}

ion-card-title {
  color: var(--ion-color-primary);
}

.test-button {
  margin: 8px 0;
}

ion-list {
  background: transparent;
}

ion-item {
  --padding-start: 16px;
  --padding-end: 16px;
  margin-bottom: 4px;
}

h3 {
  margin: 0 0 4px 0;
  font-weight: 600;
}

p {
  margin: 0;
  color: var(--ion-color-medium);
  font-size: 0.9rem;
}
