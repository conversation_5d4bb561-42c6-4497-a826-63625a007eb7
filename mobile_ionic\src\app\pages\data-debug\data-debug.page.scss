.debug-container {
  padding: 16px;
}

.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  
  ion-spinner {
    margin-bottom: 16px;
  }
  
  p {
    color: var(--ion-color-medium);
    font-size: 14px;
  }
}

.action-buttons {
  margin: 20px 0;
  
  ion-button {
    margin-bottom: 12px;
  }
}

.error-message {
  background: var(--ion-color-danger-tint);
  color: var(--ion-color-danger-contrast);
  padding: 12px;
  border-radius: 8px;
  margin: 8px 0;
  
  p {
    margin: 0;
    font-size: 14px;
  }
}

ion-card {
  margin-bottom: 16px;
}

ion-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

ion-item {
  --padding-start: 0;
  --inner-padding-end: 0;
}

ion-item ion-label h3 {
  font-weight: 600;
  margin-bottom: 4px;
}

ion-item ion-label p {
  color: var(--ion-color-medium);
  font-size: 12px;
  margin: 0;
}
