<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>Login Diagnostics</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="runDiagnostics()" [disabled]="isRunning">
        <ion-icon name="refresh"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Login Debug</ion-title>
    </ion-toolbar>
  </ion-header>

  <div class="diagnostic-container">
    
    <!-- Running State -->
    <div *ngIf="isRunning" class="loading-container">
      <ion-spinner></ion-spinner>
      <p>Running diagnostics...</p>
    </div>

    <!-- Diagnostic Results -->
    <ion-card *ngIf="!isRunning && diagnostics.length > 0">
      <ion-card-header>
        <ion-card-title>Diagnostic Results</ion-card-title>
        <ion-card-subtitle>Mobile login troubleshooting</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        
        <ion-item 
          *ngFor="let diagnostic of diagnostics" 
          (click)="showDetails(diagnostic)"
          button>
          <ion-icon 
            [name]="getStatusIcon(diagnostic.status)" 
            [color]="getStatusColor(diagnostic.status)"
            slot="start">
          </ion-icon>
          <ion-label>
            <h3>{{ diagnostic.test }}</h3>
            <p>{{ diagnostic.message }}</p>
          </ion-label>
          <ion-icon name="chevron-forward" slot="end"></ion-icon>
        </ion-item>

      </ion-card-content>
    </ion-card>

    <!-- Test Credentials -->
    <ion-card *ngIf="!isRunning">
      <ion-card-header>
        <ion-card-title>Test Credentials</ion-card-title>
        <ion-card-subtitle>Credentials used for API testing</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        
        <ion-item>
          <ion-label position="floating">Test Email</ion-label>
          <ion-input [(ngModel)]="testCredentials.email" type="email"></ion-input>
        </ion-item>

        <ion-item>
          <ion-label position="floating">Test Password</ion-label>
          <ion-input [(ngModel)]="testCredentials.password" type="password"></ion-input>
        </ion-item>

        <p class="note">
          <ion-icon name="information-circle" color="primary"></ion-icon>
          These credentials are used only for testing API connectivity. 
          A 401 error is expected and indicates the API is working correctly.
        </p>

      </ion-card-content>
    </ion-card>

    <!-- Environment Info -->
    <ion-card *ngIf="!isRunning">
      <ion-card-header>
        <ion-card-title>Environment Configuration</ion-card-title>
        <ion-card-subtitle>Current app configuration</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        
        <ion-item>
          <ion-label>
            <h3>API URL</h3>
            <p>{{ getApiUrl() }}</p>
          </ion-label>
        </ion-item>

        <ion-item>
          <ion-label>
            <h3>Production Mode</h3>
            <p>{{ isProduction() ? 'Yes' : 'No' }}</p>
          </ion-label>
        </ion-item>

        <ion-item>
          <ion-label>
            <h3>Platform</h3>
            <p>{{ getPlatformInfo() }}</p>
          </ion-label>
        </ion-item>

      </ion-card-content>
    </ion-card>

    <!-- Common Issues -->
    <ion-card *ngIf="!isRunning">
      <ion-card-header>
        <ion-card-title>Common Mobile Login Issues</ion-card-title>
        <ion-card-subtitle>Troubleshooting guide</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        
        <div class="issue-item">
          <h4>🌐 Network Connectivity</h4>
          <p>Ensure your mobile device and computer are on the same WiFi network.</p>
        </div>

        <div class="issue-item">
          <h4>🔥 Firewall/Security</h4>
          <p>Check if your computer's firewall is blocking port 8000.</p>
        </div>

        <div class="issue-item">
          <h4>🖥️ Backend Server</h4>
          <p>Verify the Laravel backend is running on your computer.</p>
        </div>

        <div class="issue-item">
          <h4>📱 CORS Configuration</h4>
          <p>Ensure CORS allows requests from mobile devices.</p>
        </div>

        <div class="issue-item">
          <h4>🔧 IP Address</h4>
          <p>Verify the IP address in environment.ts matches your computer's IP.</p>
        </div>

      </ion-card-content>
    </ion-card>

  </div>
</ion-content>

<style>
.diagnostic-container {
  padding: 16px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

ion-card {
  margin-bottom: 16px;
}

ion-item {
  --padding-start: 0;
  --inner-padding-end: 0;
}

.note {
  margin-top: 16px;
  padding: 12px;
  background: #f0f8ff;
  border-radius: 8px;
  font-size: 14px;
  color: #666;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.issue-item {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.issue-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.issue-item h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.issue-item p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}
</style>
