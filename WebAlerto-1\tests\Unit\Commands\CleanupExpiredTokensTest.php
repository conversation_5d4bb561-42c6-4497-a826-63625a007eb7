<?php

namespace Tests\Unit\Commands;

use Tests\TestCase;
use App\Console\Commands\CleanupExpiredTokens;
use App\Models\DeviceToken;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;

class CleanupExpiredTokensTest extends TestCase
{
    use RefreshDatabase;

    public function test_command_cleans_up_expired_tokens()
    {
        // Create active and not expired token
        DeviceToken::create([
            'token' => 'active_token',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true,
            'last_used_at' => now()
        ]);

        // Create expired token
        DeviceToken::create([
            'token' => 'expired_token',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true,
            'last_used_at' => now()->subDays(31)
        ]);

        // Create inactive token
        DeviceToken::create([
            'token' => 'inactive_token',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => false,
            'last_used_at' => now()
        ]);

        $this->artisan('fcm:cleanup-tokens')
            ->expectsOutput('Cleaning up tokens inactive for more than 30 days...')
            ->expectsOutput('Found 1 expired tokens')
            ->expectsOutput('Successfully deactivated 1 expired tokens')
            ->assertExitCode(0);

        $this->assertTrue(DeviceToken::where('token', 'active_token')->first()->is_active);
        $this->assertFalse(DeviceToken::where('token', 'expired_token')->first()->is_active);
        $this->assertFalse(DeviceToken::where('token', 'inactive_token')->first()->is_active);
    }

    public function test_command_cleans_up_tokens_with_custom_days()
    {
        // Create token that will be considered expired with custom days
        DeviceToken::create([
            'token' => 'expired_token',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true,
            'last_used_at' => now()->subDays(15)
        ]);

        $this->artisan('fcm:cleanup-tokens --days=10')
            ->expectsOutput('Cleaning up tokens inactive for more than 10 days...')
            ->expectsOutput('Found 1 expired tokens')
            ->expectsOutput('Successfully deactivated 1 expired tokens')
            ->assertExitCode(0);

        $this->assertFalse(DeviceToken::where('token', 'expired_token')->first()->is_active);
    }

    public function test_command_handles_no_expired_tokens()
    {
        // Create only active and not expired token
        DeviceToken::create([
            'token' => 'active_token',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true,
            'last_used_at' => now()
        ]);

        $this->artisan('fcm:cleanup-tokens')
            ->expectsOutput('Cleaning up tokens inactive for more than 30 days...')
            ->expectsOutput('Found 0 expired tokens')
            ->expectsOutput('Successfully deactivated 0 expired tokens')
            ->assertExitCode(0);

        $this->assertTrue(DeviceToken::where('token', 'active_token')->first()->is_active);
    }

    public function test_command_handles_database_errors()
    {
        // Create expired token
        DeviceToken::create([
            'token' => 'expired_token',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true,
            'last_used_at' => now()->subDays(31)
        ]);

        // Mock database error
        $this->mock(\App\Models\DeviceToken::class, function ($mock) {
            $mock->shouldReceive('deactivate')
                ->andThrow(new \Exception('Database error'));
        });

        $this->artisan('fcm:cleanup-tokens')
            ->expectsOutput('Cleaning up tokens inactive for more than 30 days...')
            ->expectsOutput('Found 1 expired tokens')
            ->expectsOutput('Successfully deactivated 0 expired tokens')
            ->assertExitCode(0);
    }

    public function test_command_logs_errors()
    {
        // Create expired token
        DeviceToken::create([
            'token' => 'expired_token',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true,
            'last_used_at' => now()->subDays(31)
        ]);

        // Mock database error
        $this->mock(\App\Models\DeviceToken::class, function ($mock) {
            $mock->shouldReceive('deactivate')
                ->andThrow(new \Exception('Database error'));
        });

        $this->artisan('fcm:cleanup-tokens');

        $this->assertDatabaseHas('logs', [
            'level' => 'error',
            'message' => 'Failed to deactivate expired token',
            'context' => json_encode([
                'token_hash' => hash('sha256', 'expired_token'),
                'error' => 'Database error'
            ])
        ]);
    }
} 