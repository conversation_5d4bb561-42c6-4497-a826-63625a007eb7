.modal-container {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  width: 100%;
  max-width: 350px;
  margin: 0 auto;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;

  ion-icon {
    font-size: 24px;
    background: white;
    border-radius: 50%;
  }
}

.center-name {
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  margin: 15px 15px 10px;
  color: #000;
}

.center-image {
  width: 100%;
  height: 160px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.info-section {
  padding: 10px 15px;
  border-bottom: 1px solid #f0f0f0;

  &:last-of-type {
    border-bottom: none;
  }
}

.info-label {
  font-size: 14px;
  color: #0099ff;
  margin-bottom: 5px;
}

.info-value {
  display: flex;
  align-items: center;
  font-size: 15px;
  color: #333;

  ion-icon {
    margin-right: 8px;
    font-size: 18px;
    min-width: 18px;
  }

  &.contact {
    ion-icon {
      color: #333;
    }
  }

  &.address {
    ion-icon {
      color: #ff4961;
    }
  }
}

.directions-button {
  padding: 10px 15px 15px;

  ion-button {
    --border-radius: 8px;
    --background: #0099ff;
    font-weight: 500;
    margin: 0;

    ion-icon {
      margin-right: 5px;
    }
  }
}
