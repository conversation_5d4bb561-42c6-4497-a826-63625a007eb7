<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Providers\FCMServiceProvider;
use Illuminate\Foundation\Application;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging;

class FCMServiceProviderTest extends TestCase
{
    protected $app;
    protected $provider;

    protected function setUp(): void
    {
        parent::setUp();
        $this->app = new Application();
        $this->provider = new FCMServiceProvider($this->app);
    }

    public function test_service_provider_registers_fcm_service()
    {
        $this->provider->register();

        $this->assertTrue($this->app->bound('fcm'));
        $this->assertInstanceOf(Messaging::class, $this->app->make('fcm'));
    }

    public function test_service_provider_registers_fcm_factory()
    {
        $this->provider->register();

        $this->assertTrue($this->app->bound('fcm.factory'));
        $this->assertInstanceOf(Factory::class, $this->app->make('fcm.factory'));
    }

    public function test_service_provider_registers_fcm_config()
    {
        $this->provider->register();

        $this->assertTrue($this->app->bound('fcm.config'));
        $this->assertIsArray($this->app->make('fcm.config'));
    }

    public function test_service_provider_registers_fcm_api_key()
    {
        $this->provider->register();

        $this->assertTrue($this->app->bound('fcm.api_key'));
        $this->assertEquals(config('fcm.api_key'), $this->app->make('fcm.api_key'));
    }

    public function test_service_provider_registers_fcm_project_id()
    {
        $this->provider->register();

        $this->assertTrue($this->app->bound('fcm.project_id'));
        $this->assertEquals(config('fcm.project_id'), $this->app->make('fcm.project_id'));
    }

    public function test_service_provider_registers_fcm_private_key()
    {
        $this->provider->register();

        $this->assertTrue($this->app->bound('fcm.private_key'));
        $this->assertEquals(config('fcm.private_key'), $this->app->make('fcm.private_key'));
    }

    public function test_service_provider_registers_fcm_client_email()
    {
        $this->provider->register();

        $this->assertTrue($this->app->bound('fcm.client_email'));
        $this->assertEquals(config('fcm.client_email'), $this->app->make('fcm.client_email'));
    }

    public function test_service_provider_registers_fcm_database_url()
    {
        $this->provider->register();

        $this->assertTrue($this->app->bound('fcm.database_url'));
        $this->assertEquals(config('fcm.database_url'), $this->app->make('fcm.database_url'));
    }

    public function test_service_provider_registers_fcm_storage_bucket()
    {
        $this->provider->register();

        $this->assertTrue($this->app->bound('fcm.storage_bucket'));
        $this->assertEquals(config('fcm.storage_bucket'), $this->app->make('fcm.storage_bucket'));
    }

    public function test_service_provider_registers_fcm_messaging()
    {
        $this->provider->register();

        $this->assertTrue($this->app->bound('fcm.messaging'));
        $this->assertInstanceOf(Messaging::class, $this->app->make('fcm.messaging'));
    }

    public function test_service_provider_registers_fcm_messaging_with_custom_config()
    {
        $this->app['config']->set('fcm.messaging', [
            'default_ttl' => 3600,
            'default_android_ttl' => 7200,
            'default_apns_ttl' => 3600
        ]);

        $this->provider->register();

        $messaging = $this->app->make('fcm.messaging');
        $this->assertInstanceOf(Messaging::class, $messaging);
    }

    public function test_service_provider_registers_fcm_messaging_with_custom_credentials()
    {
        $this->app['config']->set('fcm.credentials', [
            'file' => storage_path('firebase-credentials.json')
        ]);

        $this->provider->register();

        $messaging = $this->app->make('fcm.messaging');
        $this->assertInstanceOf(Messaging::class, $messaging);
    }

    public function test_service_provider_registers_fcm_messaging_with_custom_http_client()
    {
        $this->app['config']->set('fcm.http_client', [
            'timeout' => 30,
            'connect_timeout' => 10
        ]);

        $this->provider->register();

        $messaging = $this->app->make('fcm.messaging');
        $this->assertInstanceOf(Messaging::class, $messaging);
    }

    public function test_service_provider_registers_fcm_messaging_with_custom_retry_config()
    {
        $this->app['config']->set('fcm.retry', [
            'max_attempts' => 3,
            'delay' => 1000
        ]);

        $this->provider->register();

        $messaging = $this->app->make('fcm.messaging');
        $this->assertInstanceOf(Messaging::class, $messaging);
    }
} 