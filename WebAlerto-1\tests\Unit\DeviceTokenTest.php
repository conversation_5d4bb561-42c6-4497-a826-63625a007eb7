<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\DeviceToken;
use Illuminate\Foundation\Testing\RefreshDatabase;

class DeviceTokenTest extends TestCase
{
    use RefreshDatabase;

    public function test_token_is_valid_when_active_and_not_expired()
    {
        $token = DeviceToken::create([
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true,
            'last_used_at' => now()
        ]);

        $this->assertTrue($token->isValid());
    }

    public function test_token_is_invalid_when_inactive()
    {
        $token = DeviceToken::create([
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => false,
            'last_used_at' => now()
        ]);

        $this->assertFalse($token->isValid());
    }

    public function test_token_is_invalid_when_expired()
    {
        $token = DeviceToken::create([
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true,
            'last_used_at' => now()->subDays(31)
        ]);

        $this->assertFalse($token->isValid());
    }

    public function test_deactivate_sets_inactive_and_deactivated_at()
    {
        $token = DeviceToken::create([
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true
        ]);

        $token->deactivate();

        $this->assertFalse($token->is_active);
        $this->assertNotNull($token->deactivated_at);
    }

    public function test_reactivate_sets_active_and_clears_deactivated_at()
    {
        $token = DeviceToken::create([
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => false,
            'deactivated_at' => now()
        ]);

        $token->reactivate();

        $this->assertTrue($token->is_active);
        $this->assertNull($token->deactivated_at);
    }

    public function test_mark_as_used_updates_last_used_at()
    {
        $token = DeviceToken::create([
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true,
            'last_used_at' => now()->subDay()
        ]);

        $oldLastUsedAt = $token->last_used_at;
        $token->markAsUsed();

        $this->assertNotEquals($oldLastUsedAt, $token->last_used_at);
    }

    public function test_active_scope_returns_only_active_and_not_expired_tokens()
    {
        // Create active and not expired token
        DeviceToken::create([
            'token' => 'active_token',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true,
            'last_used_at' => now()
        ]);

        // Create inactive token
        DeviceToken::create([
            'token' => 'inactive_token',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => false,
            'last_used_at' => now()
        ]);

        // Create expired token
        DeviceToken::create([
            'token' => 'expired_token',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true,
            'last_used_at' => now()->subDays(31)
        ]);

        $activeTokens = DeviceToken::active()->get();

        $this->assertCount(1, $activeTokens);
        $this->assertEquals('active_token', $activeTokens->first()->token);
    }

    public function test_by_project_scope_returns_only_tokens_for_specific_project()
    {
        // Create tokens for different projects
        DeviceToken::create([
            'token' => 'project1_token',
            'device_type' => 'android',
            'project_id' => 'project1',
            'is_active' => true
        ]);

        DeviceToken::create([
            'token' => 'project2_token',
            'device_type' => 'android',
            'project_id' => 'project2',
            'is_active' => true
        ]);

        $project1Tokens = DeviceToken::byProject('project1')->get();

        $this->assertCount(1, $project1Tokens);
        $this->assertEquals('project1_token', $project1Tokens->first()->token);
    }

    public function test_by_device_type_scope_returns_only_tokens_for_specific_device_type()
    {
        // Create tokens for different device types
        DeviceToken::create([
            'token' => 'android_token',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true
        ]);

        DeviceToken::create([
            'token' => 'ios_token',
            'device_type' => 'ios',
            'project_id' => 'test_project',
            'is_active' => true
        ]);

        $androidTokens = DeviceToken::byDeviceType('android')->get();

        $this->assertCount(1, $androidTokens);
        $this->assertEquals('android_token', $androidTokens->first()->token);
    }

    public function test_token_hash_attribute_returns_hashed_token()
    {
        $token = DeviceToken::create([
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true
        ]);

        $this->assertEquals(hash('sha256', 'test_token_123'), $token->token_hash);
    }
} 