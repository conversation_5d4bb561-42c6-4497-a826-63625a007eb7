<?php

namespace Tests\Unit\Requests;

use Tests\TestCase;
use App\Http\Requests\BulkFCMRequest;
use Illuminate\Support\Facades\Validator;

class BulkFCMRequestTest extends TestCase
{
    protected $rules;

    protected function setUp(): void
    {
        parent::setUp();
        $this->rules = (new BulkFCMRequest())->rules();
    }

    public function test_validation_passes_with_valid_data()
    {
        $validator = Validator::make([
            'notifications' => [
                [
                    'title' => 'Test Notification 1',
                    'message' => 'This is test notification 1',
                    'severity' => 'normal',
                    'category' => 'test'
                ],
                [
                    'title' => 'Test Notification 2',
                    'message' => 'This is test notification 2',
                    'severity' => 'high',
                    'category' => 'test'
                ]
            ]
        ], $this->rules);

        $this->assertTrue($validator->passes());
    }

    public function test_validation_fails_without_notifications()
    {
        $validator = Validator::make([], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('notifications', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_empty_notifications()
    {
        $validator = Validator::make([
            'notifications' => []
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('notifications', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_too_many_notifications()
    {
        $notifications = [];
        for ($i = 0; $i < 101; $i++) {
            $notifications[] = [
                'title' => "Test Notification {$i}",
                'message' => "This is test notification {$i}",
                'severity' => 'normal',
                'category' => 'test'
            ];
        }

        $validator = Validator::make([
            'notifications' => $notifications
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('notifications', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_invalid_notification_data()
    {
        $validator = Validator::make([
            'notifications' => [
                [
                    'title' => '',
                    'message' => '',
                    'severity' => 'invalid',
                    'category' => ''
                ]
            ]
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('notifications.0.title', $validator->errors()->toArray());
        $this->assertArrayHasKey('notifications.0.message', $validator->errors()->toArray());
        $this->assertArrayHasKey('notifications.0.severity', $validator->errors()->toArray());
        $this->assertArrayHasKey('notifications.0.category', $validator->errors()->toArray());
    }

    public function test_validation_passes_with_valid_severity_values()
    {
        $validSeverities = ['normal', 'high', 'urgent'];

        foreach ($validSeverities as $severity) {
            $validator = Validator::make([
                'notifications' => [
                    [
                        'title' => 'Test Notification',
                        'message' => 'This is a test notification',
                        'severity' => $severity,
                        'category' => 'test'
                    ]
                ]
            ], $this->rules);

            $this->assertTrue($validator->passes(), "Validation should pass for severity: {$severity}");
        }
    }

    public function test_validation_fails_with_too_long_title()
    {
        $validator = Validator::make([
            'notifications' => [
                [
                    'title' => str_repeat('a', 256),
                    'message' => 'This is a test notification',
                    'severity' => 'normal',
                    'category' => 'test'
                ]
            ]
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('notifications.0.title', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_too_long_message()
    {
        $validator = Validator::make([
            'notifications' => [
                [
                    'title' => 'Test Notification',
                    'message' => str_repeat('a', 4097),
                    'severity' => 'normal',
                    'category' => 'test'
                ]
            ]
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('notifications.0.message', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_too_long_category()
    {
        $validator = Validator::make([
            'notifications' => [
                [
                    'title' => 'Test Notification',
                    'message' => 'This is a test notification',
                    'severity' => 'normal',
                    'category' => str_repeat('a', 256)
                ]
            ]
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('notifications.0.category', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_missing_required_fields()
    {
        $validator = Validator::make([
            'notifications' => [
                [
                    'severity' => 'normal',
                    'category' => 'test'
                ]
            ]
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('notifications.0.title', $validator->errors()->toArray());
        $this->assertArrayHasKey('notifications.0.message', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_invalid_notification_structure()
    {
        $validator = Validator::make([
            'notifications' => [
                'invalid_structure'
            ]
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('notifications.0', $validator->errors()->toArray());
    }
} 