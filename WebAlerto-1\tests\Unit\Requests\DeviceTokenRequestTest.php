<?php

namespace Tests\Unit\Requests;

use Tests\TestCase;
use App\Http\Requests\DeviceTokenRequest;
use Illuminate\Support\Facades\Validator;

class DeviceTokenRequestTest extends TestCase
{
    protected $rules;

    protected function setUp(): void
    {
        parent::setUp();
        $this->rules = (new DeviceTokenRequest())->rules();
    }

    public function test_validation_passes_with_valid_data()
    {
        $validator = Validator::make([
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'test_project'
        ], $this->rules);

        $this->assertTrue($validator->passes());
    }

    public function test_validation_fails_without_token()
    {
        $validator = Validator::make([
            'device_type' => 'android',
            'project_id' => 'test_project'
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('token', $validator->errors()->toArray());
    }

    public function test_validation_fails_without_device_type()
    {
        $validator = Validator::make([
            'token' => 'test_token_123',
            'project_id' => 'test_project'
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('device_type', $validator->errors()->toArray());
    }

    public function test_validation_fails_without_project_id()
    {
        $validator = Validator::make([
            'token' => 'test_token_123',
            'device_type' => 'android'
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('project_id', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_invalid_device_type()
    {
        $validator = Validator::make([
            'token' => 'test_token_123',
            'device_type' => 'invalid',
            'project_id' => 'test_project'
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('device_type', $validator->errors()->toArray());
    }

    public function test_validation_passes_with_valid_device_type_values()
    {
        $validDeviceTypes = ['android', 'ios', 'web'];

        foreach ($validDeviceTypes as $deviceType) {
            $validator = Validator::make([
                'token' => 'test_token_123',
                'device_type' => $deviceType,
                'project_id' => 'test_project'
            ], $this->rules);

            $this->assertTrue($validator->passes(), "Validation should pass for device type: {$deviceType}");
        }
    }

    public function test_validation_fails_with_empty_token()
    {
        $validator = Validator::make([
            'token' => '',
            'device_type' => 'android',
            'project_id' => 'test_project'
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('token', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_empty_device_type()
    {
        $validator = Validator::make([
            'token' => 'test_token_123',
            'device_type' => '',
            'project_id' => 'test_project'
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('device_type', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_empty_project_id()
    {
        $validator = Validator::make([
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => ''
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('project_id', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_too_long_token()
    {
        $validator = Validator::make([
            'token' => str_repeat('a', 4097),
            'device_type' => 'android',
            'project_id' => 'test_project'
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('token', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_too_long_project_id()
    {
        $validator = Validator::make([
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => str_repeat('a', 256)
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('project_id', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_invalid_project_id_format()
    {
        $validator = Validator::make([
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'invalid project id'
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('project_id', $validator->errors()->toArray());
    }

    public function test_validation_passes_with_valid_project_id_format()
    {
        $validProjectIds = [
            'test-project',
            'test_project',
            'test.project',
            'test123',
            'test-123',
            'test_123',
            'test.123'
        ];

        foreach ($validProjectIds as $projectId) {
            $validator = Validator::make([
                'token' => 'test_token_123',
                'device_type' => 'android',
                'project_id' => $projectId
            ], $this->rules);

            $this->assertTrue($validator->passes(), "Validation should pass for project ID: {$projectId}");
        }
    }
} 