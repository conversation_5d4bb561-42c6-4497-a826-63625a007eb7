<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>Network Diagnostics</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="runDiagnostics()" [disabled]="isRunning">
        <ion-icon name="refresh"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Network Diagnostics</ion-title>
    </ion-toolbar>
  </ion-header>

  <div class="diagnostics-container">
    
    <!-- Network Info -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>Network Information</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-item>
          <ion-label>
            <h3>Platform</h3>
            <p>{{ networkInfo.platform }}</p>
          </ion-label>
        </ion-item>
        <ion-item *ngIf="networkInfo.online !== undefined">
          <ion-label>
            <h3>Online Status</h3>
            <p>{{ networkInfo.online ? 'Online' : 'Offline' }}</p>
          </ion-label>
          <ion-icon 
            [name]="networkInfo.online ? 'wifi' : 'wifi-off'" 
            [color]="networkInfo.online ? 'success' : 'danger'"
            slot="end">
          </ion-icon>
        </ion-item>
      </ion-card-content>
    </ion-card>

    <!-- Diagnostic Tests -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>Connectivity Tests</ion-card-title>
        <ion-card-subtitle>Testing network connections...</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        
        <!-- Internet Connectivity -->
        <ion-item>
          <ion-icon 
            [name]="getStatusIcon(diagnostics.internet.status)" 
            [color]="getStatusColor(diagnostics.internet.status)"
            slot="start">
          </ion-icon>
          <ion-label>
            <h3>Internet Connection</h3>
            <p>{{ diagnostics.internet.message }}</p>
          </ion-label>
        </ion-item>

        <!-- Backend Connectivity -->
        <ion-item>
          <ion-icon 
            [name]="getStatusIcon(diagnostics.backend.status)" 
            [color]="getStatusColor(diagnostics.backend.status)"
            slot="start">
          </ion-icon>
          <ion-label>
            <h3>Backend Server</h3>
            <p>{{ diagnostics.backend.message }}</p>
          </ion-label>
        </ion-item>

        <!-- Routing Service Connectivity -->
        <ion-item>
          <ion-icon 
            [name]="getStatusIcon(diagnostics.routing.status)" 
            [color]="getStatusColor(diagnostics.routing.status)"
            slot="start">
          </ion-icon>
          <ion-label>
            <h3>Routing Service</h3>
            <p>{{ diagnostics.routing.message }}</p>
          </ion-label>
        </ion-item>

      </ion-card-content>
    </ion-card>

    <!-- Help Section -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>Troubleshooting</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-button expand="block" fill="outline" (click)="showNetworkHelp()">
          <ion-icon name="help-circle" slot="start"></ion-icon>
          Show Network Help
        </ion-button>
        
        <ion-button expand="block" (click)="runDiagnostics()" [disabled]="isRunning">
          <ion-icon name="refresh" slot="start"></ion-icon>
          Run Diagnostics Again
        </ion-button>
      </ion-card-content>
    </ion-card>

  </div>
</ion-content>

<style>
.diagnostics-container {
  padding: 16px;
}

ion-card {
  margin-bottom: 16px;
}

ion-item {
  --padding-start: 0;
  --inner-padding-end: 0;
}
</style>
