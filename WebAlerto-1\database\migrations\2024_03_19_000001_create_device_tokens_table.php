<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // Only create the table if it doesn't exist
        if (!Schema::hasTable('device_tokens')) {
            Schema::create('device_tokens', function (Blueprint $table) {
                $table->id();
                $table->string('token')->unique();
                $table->string('device_type');
                $table->string('project_id');
                $table->boolean('is_active')->default(true);
                $table->timestamp('last_used_at')->nullable();
                $table->timestamp('deactivated_at')->nullable();
                $table->timestamps();

                $table->index(['project_id', 'device_type']);
                $table->index('is_active');
                $table->index('last_used_at');
            });
        } else {
            // If the table exists, make sure it has all the required columns and indexes
            $columns = Schema::getColumnListing('device_tokens');

            // Add missing columns if needed
            if (!in_array('device_type', $columns)) {
                Schema::table('device_tokens', function (Blueprint $table) {
                    $table->string('device_type')->after('token');
                });
            }

            if (!in_array('project_id', $columns)) {
                Schema::table('device_tokens', function (Blueprint $table) {
                    $table->string('project_id')->after('device_type');
                });
            }

            if (!in_array('is_active', $columns)) {
                Schema::table('device_tokens', function (Blueprint $table) {
                    $table->boolean('is_active')->default(true)->after('project_id');
                });
            }

            if (!in_array('last_used_at', $columns)) {
                Schema::table('device_tokens', function (Blueprint $table) {
                    $table->timestamp('last_used_at')->nullable()->after('is_active');
                });
            }

            if (!in_array('deactivated_at', $columns)) {
                Schema::table('device_tokens', function (Blueprint $table) {
                    $table->timestamp('deactivated_at')->nullable()->after('last_used_at');
                });
            }

            // Add indexes if they don't exist
            // Note: We can't easily check if indexes exist, so we'll try to add them and catch any exceptions
            try {
                Schema::table('device_tokens', function (Blueprint $table) {
                    $table->index(['project_id', 'device_type']);
                });
            } catch (\Exception $e) {
                // Index already exists or can't be created
            }

            try {
                Schema::table('device_tokens', function (Blueprint $table) {
                    $table->index('is_active');
                });
            } catch (\Exception $e) {
                // Index already exists or can't be created
            }

            try {
                Schema::table('device_tokens', function (Blueprint $table) {
                    $table->index('last_used_at');
                });
            } catch (\Exception $e) {
                // Index already exists or can't be created
            }
        }
    }

    public function down()
    {
        Schema::dropIfExists('device_tokens');
    }
};