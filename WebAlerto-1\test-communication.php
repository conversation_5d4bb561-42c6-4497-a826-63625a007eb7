<?php
/**
 * Simple communication test script
 * This script tests the basic communication setup between components
 */

// Set headers for API response
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Get the request path
$requestUri = $_SERVER['REQUEST_URI'];
$path = parse_url($requestUri, PHP_URL_PATH);

// Simple routing
switch ($path) {
    case '/test':
    case '/api/test':
        echo json_encode([
            'success' => true,
            'message' => 'WebAlerto API is working!',
            'timestamp' => date('Y-m-d H:i:s'),
            'server_ip' => $_SERVER['SERVER_ADDR'] ?? 'unknown',
            'client_ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ]);
        break;
        
    case '/test/firebase':
    case '/api/test/firebase':
        echo json_encode([
            'success' => true,
            'message' => 'Firebase configuration test',
            'firebase_config' => [
                'project_id' => 'last-5acaf',
                'messaging_sender_id' => '************',
                'app_id' => '1:************:android:c7c81cb0ccca4f30cb7815'
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        break;
        
    case '/test/database':
    case '/api/test/database':
        // Simple database connection test
        try {
            $host = '127.0.0.1';
            $dbname = 'lastNa';
            $username = 'root';
            $password = 'root';
            
            $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            echo json_encode([
                'success' => true,
                'message' => 'Database connection successful',
                'database' => $dbname,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        } catch (PDOException $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Database connection failed',
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        }
        break;
        
    default:
        echo json_encode([
            'success' => true,
            'message' => 'WebAlerto Communication Test Server',
            'available_endpoints' => [
                '/test' => 'Basic API test',
                '/test/firebase' => 'Firebase configuration test',
                '/test/database' => 'Database connection test'
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        break;
}
?>
