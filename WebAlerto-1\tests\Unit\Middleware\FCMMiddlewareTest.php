<?php

namespace Tests\Unit\Middleware;

use Tests\TestCase;
use App\Http\Middleware\FCMMiddleware;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Mockery;

class FCMMiddlewareTest extends TestCase
{
    protected $middleware;
    protected $request;

    protected function setUp(): void
    {
        parent::setUp();
        $this->middleware = new FCMMiddleware();
        $this->request = new Request();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_middleware_allows_request_with_valid_api_key()
    {
        $this->request->headers->set('X-API-Key', config('fcm.api_key'));

        $response = $this->middleware->handle($this->request, function ($request) {
            return response()->json(['success' => true]);
        });

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals(['success' => true], json_decode($response->getContent(), true));
    }

    public function test_middleware_blocks_request_without_api_key()
    {
        $response = $this->middleware->handle($this->request, function ($request) {
            return response()->json(['success' => true]);
        });

        $this->assertEquals(401, $response->getStatusCode());
        $this->assertEquals([
            'success' => false,
            'message' => 'Unauthorized: Missing API key'
        ], json_decode($response->getContent(), true));
    }

    public function test_middleware_blocks_request_with_invalid_api_key()
    {
        $this->request->headers->set('X-API-Key', 'invalid_key');

        $response = $this->middleware->handle($this->request, function ($request) {
            return response()->json(['success' => true]);
        });

        $this->assertEquals(401, $response->getStatusCode());
        $this->assertEquals([
            'success' => false,
            'message' => 'Unauthorized: Invalid API key'
        ], json_decode($response->getContent(), true));
    }

    public function test_middleware_respects_rate_limits()
    {
        $this->request->headers->set('X-API-Key', config('fcm.api_key'));

        RateLimiter::shouldReceive('tooManyAttempts')
            ->once()
            ->andReturn(true);

        RateLimiter::shouldReceive('availableIn')
            ->once()
            ->andReturn(60);

        $response = $this->middleware->handle($this->request, function ($request) {
            return response()->json(['success' => true]);
        });

        $this->assertEquals(429, $response->getStatusCode());
        $this->assertEquals([
            'success' => false,
            'message' => 'Too many requests. Please try again in 60 seconds.'
        ], json_decode($response->getContent(), true));
    }

    public function test_middleware_increments_rate_limit_counter()
    {
        $this->request->headers->set('X-API-Key', config('fcm.api_key'));

        RateLimiter::shouldReceive('tooManyAttempts')
            ->once()
            ->andReturn(false);

        RateLimiter::shouldReceive('hit')
            ->once();

        $response = $this->middleware->handle($this->request, function ($request) {
            return response()->json(['success' => true]);
        });

        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_middleware_handles_exceptions()
    {
        $this->request->headers->set('X-API-Key', config('fcm.api_key'));

        $response = $this->middleware->handle($this->request, function ($request) {
            throw new \Exception('Test exception');
        });

        $this->assertEquals(500, $response->getStatusCode());
        $this->assertEquals([
            'success' => false,
            'message' => 'Internal server error'
        ], json_decode($response->getContent(), true));
    }

    public function test_middleware_handles_rate_limit_exceptions()
    {
        $this->request->headers->set('X-API-Key', config('fcm.api_key'));

        RateLimiter::shouldReceive('tooManyAttempts')
            ->once()
            ->andThrow(new \Exception('Rate limit error'));

        $response = $this->middleware->handle($this->request, function ($request) {
            return response()->json(['success' => true]);
        });

        $this->assertEquals(500, $response->getStatusCode());
        $this->assertEquals([
            'success' => false,
            'message' => 'Internal server error'
        ], json_decode($response->getContent(), true));
    }

    public function test_middleware_handles_rate_limit_increment_exceptions()
    {
        $this->request->headers->set('X-API-Key', config('fcm.api_key'));

        RateLimiter::shouldReceive('tooManyAttempts')
            ->once()
            ->andReturn(false);

        RateLimiter::shouldReceive('hit')
            ->once()
            ->andThrow(new \Exception('Rate limit increment error'));

        $response = $this->middleware->handle($this->request, function ($request) {
            return response()->json(['success' => true]);
        });

        $this->assertEquals(500, $response->getStatusCode());
        $this->assertEquals([
            'success' => false,
            'message' => 'Internal server error'
        ], json_decode($response->getContent(), true));
    }

    public function test_middleware_handles_invalid_json_response()
    {
        $this->request->headers->set('X-API-Key', config('fcm.api_key'));

        $response = $this->middleware->handle($this->request, function ($request) {
            return response()->json(['success' => "\xB1\x31"]); // Invalid UTF-8 sequence
        });

        $this->assertEquals(500, $response->getStatusCode());
        $this->assertEquals([
            'success' => false,
            'message' => 'Internal server error'
        ], json_decode($response->getContent(), true));
    }
} 