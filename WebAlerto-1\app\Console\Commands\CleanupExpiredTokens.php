<?php

namespace App\Console\Commands;

use App\Models\DeviceToken;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CleanupExpiredTokens extends Command
{
    protected $signature = 'fcm:cleanup-tokens {--days=30 : Number of days of inactivity before token is considered expired}';
    protected $description = 'Clean up expired FCM device tokens';

    public function handle()
    {
        $days = $this->option('days');
        $this->info("Cleaning up tokens inactive for more than {$days} days...");

        $expiredTokens = DeviceToken::where('is_active', true)
            ->where('last_used_at', '<', now()->subDays($days))
            ->get();

        $count = $expiredTokens->count();
        $this->info("Found {$count} expired tokens");

        $bar = $this->output->createProgressBar($count);
        $bar->start();

        foreach ($expiredTokens as $token) {
            try {
                $token->deactivate();
                $bar->advance();
            } catch (\Exception $e) {
                Log::error('Failed to deactivate expired token', [
                    'token_hash' => $token->token_hash,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $bar->finish();
        $this->newLine();
        $this->info("Successfully deactivated {$count} expired tokens");

        return Command::SUCCESS;
    }
} 