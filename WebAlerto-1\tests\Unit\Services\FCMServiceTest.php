<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\FCMService;
use App\Exceptions\FCMException;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Message;
use Kreait\Firebase\Messaging\Notification;
use Kreait\Firebase\Messaging\AndroidConfig;
use Kreait\Firebase\Messaging\ApnsConfig;
use Kreait\Firebase\Messaging\WebPushConfig;
use Kreait\Firebase\Messaging\MessageTarget;
use Kreait\Firebase\Messaging\MulticastSendReport;
use Kreait\Firebase\Messaging\SendReport;
use Kreait\Firebase\Exception\MessagingException;
use Mockery;

class FCMServiceTest extends TestCase
{
    protected $messaging;
    protected $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->messaging = Mockery::mock('Kreait\Firebase\Messaging');
        $this->service = new FCMService($this->messaging);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_send_single_notification()
    {
        $token = 'test_token';
        $notification = [
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ];

        $expectedMessage = CloudMessage::withTarget(MessageTarget::TOKEN, $token)
            ->withNotification(Notification::create($notification['title'], $notification['message']))
            ->withData([
                'severity' => $notification['severity'],
                'category' => $notification['category']
            ]);

        $this->messaging->shouldReceive('send')
            ->once()
            ->with(Mockery::type(Message::class))
            ->andReturn('message_id');

        $result = $this->service->send($token, $notification);

        $this->assertEquals('message_id', $result);
    }

    public function test_send_single_notification_with_android_config()
    {
        $token = 'test_token';
        $notification = [
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test',
            'android' => [
                'priority' => 'high',
                'ttl' => '3600s',
                'notification' => [
                    'channel_id' => 'high_importance_channel',
                    'sound' => 'default',
                    'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
                ]
            ]
        ];

        $expectedMessage = CloudMessage::withTarget(MessageTarget::TOKEN, $token)
            ->withNotification(Notification::create($notification['title'], $notification['message']))
            ->withData([
                'severity' => $notification['severity'],
                'category' => $notification['category']
            ])
            ->withAndroidConfig(AndroidConfig::fromArray($notification['android']));

        $this->messaging->shouldReceive('send')
            ->once()
            ->with(Mockery::type(Message::class))
            ->andReturn('message_id');

        $result = $this->service->send($token, $notification);

        $this->assertEquals('message_id', $result);
    }

    public function test_send_single_notification_with_apns_config()
    {
        $token = 'test_token';
        $notification = [
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test',
            'apns' => [
                'headers' => [
                    'apns-priority' => '10'
                ],
                'payload' => [
                    'aps' => [
                        'sound' => 'default',
                        'badge' => 1
                    ]
                ]
            ]
        ];

        $expectedMessage = CloudMessage::withTarget(MessageTarget::TOKEN, $token)
            ->withNotification(Notification::create($notification['title'], $notification['message']))
            ->withData([
                'severity' => $notification['severity'],
                'category' => $notification['category']
            ])
            ->withApnsConfig(ApnsConfig::fromArray($notification['apns']));

        $this->messaging->shouldReceive('send')
            ->once()
            ->with(Mockery::type(Message::class))
            ->andReturn('message_id');

        $result = $this->service->send($token, $notification);

        $this->assertEquals('message_id', $result);
    }

    public function test_send_single_notification_with_web_push_config()
    {
        $token = 'test_token';
        $notification = [
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test',
            'webpush' => [
                'headers' => [
                    'Urgency' => 'high'
                ],
                'notification' => [
                    'icon' => 'https://example.com/icon.png',
                    'badge' => 'https://example.com/badge.png',
                    'vibrate' => [100, 50, 100]
                ]
            ]
        ];

        $expectedMessage = CloudMessage::withTarget(MessageTarget::TOKEN, $token)
            ->withNotification(Notification::create($notification['title'], $notification['message']))
            ->withData([
                'severity' => $notification['severity'],
                'category' => $notification['category']
            ])
            ->withWebPushConfig(WebPushConfig::fromArray($notification['webpush']));

        $this->messaging->shouldReceive('send')
            ->once()
            ->with(Mockery::type(Message::class))
            ->andReturn('message_id');

        $result = $this->service->send($token, $notification);

        $this->assertEquals('message_id', $result);
    }

    public function test_send_single_notification_throws_exception()
    {
        $token = 'test_token';
        $notification = [
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ];

        $this->messaging->shouldReceive('send')
            ->once()
            ->with(Mockery::type(Message::class))
            ->andThrow(new MessagingException('Failed to send message'));

        $this->expectException(FCMException::class);
        $this->expectExceptionMessage('Failed to send message');

        $this->service->send($token, $notification);
    }

    public function test_send_multicast_notification()
    {
        $tokens = ['token1', 'token2', 'token3'];
        $notification = [
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ];

        $expectedMessage = CloudMessage::new()
            ->withNotification(Notification::create($notification['title'], $notification['message']))
            ->withData([
                'severity' => $notification['severity'],
                'category' => $notification['category']
            ]);

        $report = Mockery::mock(MulticastSendReport::class);
        $report->shouldReceive('successes')->andReturn(2);
        $report->shouldReceive('failures')->andReturn(1);
        $report->shouldReceive('hasFailures')->andReturn(true);

        $this->messaging->shouldReceive('sendMulticast')
            ->once()
            ->with(Mockery::type(Message::class), $tokens)
            ->andReturn($report);

        $result = $this->service->sendMulticast($tokens, $notification);

        $this->assertEquals(2, $result['success_count']);
        $this->assertEquals(1, $result['failure_count']);
    }

    public function test_send_multicast_notification_with_platform_configs()
    {
        $tokens = ['token1', 'token2', 'token3'];
        $notification = [
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test',
            'android' => [
                'priority' => 'high'
            ],
            'apns' => [
                'headers' => [
                    'apns-priority' => '10'
                ]
            ],
            'webpush' => [
                'headers' => [
                    'Urgency' => 'high'
                ]
            ]
        ];

        $expectedMessage = CloudMessage::new()
            ->withNotification(Notification::create($notification['title'], $notification['message']))
            ->withData([
                'severity' => $notification['severity'],
                'category' => $notification['category']
            ])
            ->withAndroidConfig(AndroidConfig::fromArray($notification['android']))
            ->withApnsConfig(ApnsConfig::fromArray($notification['apns']))
            ->withWebPushConfig(WebPushConfig::fromArray($notification['webpush']));

        $report = Mockery::mock(MulticastSendReport::class);
        $report->shouldReceive('successes')->andReturn(3);
        $report->shouldReceive('failures')->andReturn(0);
        $report->shouldReceive('hasFailures')->andReturn(false);

        $this->messaging->shouldReceive('sendMulticast')
            ->once()
            ->with(Mockery::type(Message::class), $tokens)
            ->andReturn($report);

        $result = $this->service->sendMulticast($tokens, $notification);

        $this->assertEquals(3, $result['success_count']);
        $this->assertEquals(0, $result['failure_count']);
    }

    public function test_send_multicast_notification_throws_exception()
    {
        $tokens = ['token1', 'token2', 'token3'];
        $notification = [
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ];

        $this->messaging->shouldReceive('sendMulticast')
            ->once()
            ->with(Mockery::type(Message::class), $tokens)
            ->andThrow(new MessagingException('Failed to send multicast message'));

        $this->expectException(FCMException::class);
        $this->expectExceptionMessage('Failed to send multicast message');

        $this->service->sendMulticast($tokens, $notification);
    }

    public function test_send_multicast_notification_with_detailed_results()
    {
        $tokens = ['token1', 'token2', 'token3'];
        $notification = [
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ];

        $report = Mockery::mock(MulticastSendReport::class);
        $report->shouldReceive('successes')->andReturn(2);
        $report->shouldReceive('failures')->andReturn(1);
        $report->shouldReceive('hasFailures')->andReturn(true);
        $report->shouldReceive('getItems')->andReturn([
            Mockery::mock(SendReport::class)->shouldReceive('isSuccess')->andReturn(true)->getMock(),
            Mockery::mock(SendReport::class)->shouldReceive('isSuccess')->andReturn(true)->getMock(),
            Mockery::mock(SendReport::class)->shouldReceive('isSuccess')->andReturn(false)
                ->shouldReceive('error')->andReturn('Invalid token')->getMock()
        ]);

        $this->messaging->shouldReceive('sendMulticast')
            ->once()
            ->with(Mockery::type(Message::class), $tokens)
            ->andReturn($report);

        $result = $this->service->sendMulticast($tokens, $notification, true);

        $this->assertEquals(2, $result['success_count']);
        $this->assertEquals(1, $result['failure_count']);
        $this->assertArrayHasKey('results', $result);
        $this->assertCount(3, $result['results']);
    }

    public function test_send_multicast_notification_with_empty_tokens()
    {
        $tokens = [];
        $notification = [
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ];

        $this->expectException(FCMException::class);
        $this->expectExceptionMessage('No tokens provided for multicast message');

        $this->service->sendMulticast($tokens, $notification);
    }

    public function test_send_multicast_notification_with_too_many_tokens()
    {
        $tokens = array_fill(0, 501, 'token');
        $notification = [
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ];

        $this->expectException(FCMException::class);
        $this->expectExceptionMessage('Too many tokens provided for multicast message. Maximum is 500.');

        $this->service->sendMulticast($tokens, $notification);
    }
} 