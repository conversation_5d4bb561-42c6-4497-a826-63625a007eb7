<?php

namespace Tests\Unit\Requests;

use Tests\TestCase;
use App\Http\Requests\FCMRequest;
use Illuminate\Support\Facades\Validator;

class FCMRequestTest extends TestCase
{
    protected $rules;

    protected function setUp(): void
    {
        parent::setUp();
        $this->rules = (new FCMRequest())->rules();
    }

    public function test_validation_passes_with_valid_data()
    {
        $validator = Validator::make([
            'token' => 'test_token',
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ], $this->rules);

        $this->assertTrue($validator->passes());
    }

    public function test_validation_passes_with_platform_configs()
    {
        $validator = Validator::make([
            'token' => 'test_token',
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test',
            'android' => [
                'priority' => 'high',
                'ttl' => '3600s',
                'notification' => [
                    'channel_id' => 'high_importance_channel',
                    'sound' => 'default',
                    'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
                ]
            ],
            'apns' => [
                'headers' => [
                    'apns-priority' => '10'
                ],
                'payload' => [
                    'aps' => [
                        'sound' => 'default',
                        'badge' => 1
                    ]
                ]
            ],
            'webpush' => [
                'headers' => [
                    'Urgency' => 'high'
                ],
                'notification' => [
                    'icon' => 'https://example.com/icon.png',
                    'badge' => 'https://example.com/badge.png',
                    'vibrate' => [100, 50, 100]
                ]
            ]
        ], $this->rules);

        $this->assertTrue($validator->passes());
    }

    public function test_validation_fails_without_required_fields()
    {
        $validator = Validator::make([], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('token', $validator->errors()->toArray());
        $this->assertArrayHasKey('title', $validator->errors()->toArray());
        $this->assertArrayHasKey('message', $validator->errors()->toArray());
        $this->assertArrayHasKey('severity', $validator->errors()->toArray());
        $this->assertArrayHasKey('category', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_invalid_severity()
    {
        $validator = Validator::make([
            'token' => 'test_token',
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'invalid',
            'category' => 'test'
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('severity', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_too_long_title()
    {
        $validator = Validator::make([
            'token' => 'test_token',
            'title' => str_repeat('a', 256),
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('title', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_too_long_message()
    {
        $validator = Validator::make([
            'token' => 'test_token',
            'title' => 'Test Notification',
            'message' => str_repeat('a', 4097),
            'severity' => 'normal',
            'category' => 'test'
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('message', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_too_long_category()
    {
        $validator = Validator::make([
            'token' => 'test_token',
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => str_repeat('a', 256)
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('category', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_invalid_android_config()
    {
        $validator = Validator::make([
            'token' => 'test_token',
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test',
            'android' => [
                'priority' => 'invalid'
            ]
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('android.priority', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_invalid_apns_config()
    {
        $validator = Validator::make([
            'token' => 'test_token',
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test',
            'apns' => [
                'headers' => [
                    'apns-priority' => 'invalid'
                ]
            ]
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('apns.headers.apns-priority', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_invalid_webpush_config()
    {
        $validator = Validator::make([
            'token' => 'test_token',
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test',
            'webpush' => [
                'headers' => [
                    'Urgency' => 'invalid'
                ]
            ]
        ], $this->rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('webpush.headers.Urgency', $validator->errors()->toArray());
    }

    public function test_validation_passes_with_valid_severity_values()
    {
        $validSeverities = ['normal', 'high', 'urgent'];

        foreach ($validSeverities as $severity) {
            $validator = Validator::make([
                'token' => 'test_token',
                'title' => 'Test Notification',
                'message' => 'This is a test notification',
                'severity' => $severity,
                'category' => 'test'
            ], $this->rules);

            $this->assertTrue($validator->passes(), "Validation should pass for severity: {$severity}");
        }
    }
} 