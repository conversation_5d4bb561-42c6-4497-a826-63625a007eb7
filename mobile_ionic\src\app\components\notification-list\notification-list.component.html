<ion-list>
  <ion-list-header>
    <ion-label>Notifications</ion-label>
  </ion-list-header>

  <ion-item *ngIf="notifications.length === 0">
    <ion-label color="medium">No notifications yet</ion-label>
  </ion-item>

  <ion-item *ngFor="let notification of notifications" button (click)="onNotificationClick(notification)">
    <ion-icon [name]="getNotificationIcon(notification)" slot="start" [color]="getSeverityColor(notification)"></ion-icon>
    <ion-label>
      <h2>{{ notification.title }}</h2>
      <p>{{ notification.body }}</p>
      <p *ngIf="notification['time']" class="notification-time">{{ formatTime(notification) }}</p>
    </ion-label>
  </ion-item>
</ion-list>
