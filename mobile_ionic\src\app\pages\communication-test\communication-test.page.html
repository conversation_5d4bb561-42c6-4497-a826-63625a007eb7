<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>Communication Test</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="showEnvironmentInfo()">
        <ion-icon name="information-circle-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Communication Test</ion-title>
    </ion-toolbar>
  </ion-header>

  <!-- Test Controls -->
  <ion-card>
    <ion-card-header>
      <ion-card-title>Test Controls</ion-card-title>
      <ion-card-subtitle>Check communication between mobile app and backend</ion-card-subtitle>
    </ion-card-header>
    <ion-card-content>
      <ion-button 
        expand="block" 
        (click)="runFullTest()" 
        [disabled]="isLoading"
        color="primary">
        <ion-icon name="analytics-outline" slot="start"></ion-icon>
        Run Full Communication Test
      </ion-button>
      
      <ion-button 
        expand="block" 
        fill="outline" 
        (click)="quickTest()" 
        [disabled]="isLoading"
        color="secondary">
        <ion-icon name="flash-outline" slot="start"></ion-icon>
        Quick Connectivity Test
      </ion-button>
    </ion-card-content>
  </ion-card>

  <!-- Overall Status -->
  <ion-card *ngIf="status">
    <ion-card-header>
      <ion-card-title>
        <ion-icon 
          [name]="getStatusIcon(status.overallStatus === 'connected')" 
          [color]="getStatusColor(status.overallStatus)">
        </ion-icon>
        Overall Status: {{ status.overallStatus | titlecase }}
      </ion-card-title>
      <ion-card-subtitle>Last checked: {{ status.lastChecked | date:'medium' }}</ion-card-subtitle>
    </ion-card-header>
    <ion-card-content>
      <ion-grid>
        <ion-row>
          <ion-col size="6">
            <ion-item lines="none">
              <ion-icon 
                [name]="getStatusIcon(status.backendConnected)" 
                [color]="getStatusIconColor(status.backendConnected)" 
                slot="start">
              </ion-icon>
              <ion-label>Backend</ion-label>
            </ion-item>
          </ion-col>
          <ion-col size="6">
            <ion-item lines="none">
              <ion-icon 
                [name]="getStatusIcon(status.apiWorking)" 
                [color]="getStatusIconColor(status.apiWorking)" 
                slot="start">
              </ion-icon>
              <ion-label>API</ion-label>
            </ion-item>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="6">
            <ion-item lines="none">
              <ion-icon 
                [name]="getStatusIcon(status.firebaseConfigured)" 
                [color]="getStatusIconColor(status.firebaseConfigured)" 
                slot="start">
              </ion-icon>
              <ion-label>Firebase</ion-label>
            </ion-item>
          </ion-col>
          <ion-col size="6">
            <ion-item lines="none">
              <ion-icon 
                [name]="getStatusIcon(status.databaseConnected)" 
                [color]="getStatusIconColor(status.databaseConnected)" 
                slot="start">
              </ion-icon>
              <ion-label>Database</ion-label>
            </ion-item>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-card-content>
  </ion-card>

  <!-- Detailed Results -->
  <ion-card *ngIf="status && status.results.length > 0">
    <ion-card-header>
      <ion-card-title>Test Results</ion-card-title>
      <ion-card-subtitle>Tap on any result for details</ion-card-subtitle>
    </ion-card-header>
    <ion-card-content>
      <ion-list>
        <ion-item 
          *ngFor="let result of status.results" 
          button 
          (click)="showResultDetails(result)">
          <ion-icon 
            [name]="getStatusIcon(result.success)" 
            [color]="getStatusIconColor(result.success)" 
            slot="start">
          </ion-icon>
          <ion-label>
            <h3>{{ result.endpoint }}</h3>
            <p>{{ result.message }}</p>
            <p>Response time: {{ result.responseTime }}ms</p>
          </ion-label>
          <ion-badge 
            [color]="result.success ? 'success' : 'danger'" 
            slot="end">
            {{ result.success ? 'OK' : 'FAIL' }}
          </ion-badge>
        </ion-item>
      </ion-list>
    </ion-card-content>
  </ion-card>

  <!-- Environment Info -->
  <ion-card>
    <ion-card-header>
      <ion-card-title>Current Configuration</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-item lines="none">
        <ion-label>
          <h3>API URL</h3>
          <p>{{ environment.apiUrl }}</p>
        </ion-label>
      </ion-item>
      <ion-item lines="none">
        <ion-label>
          <h3>Firebase Project</h3>
          <p>{{ environment.firebase.projectId }}</p>
        </ion-label>
      </ion-item>
      <ion-item lines="none">
        <ion-label>
          <h3>Production Mode</h3>
          <p>{{ environment.production ? 'Yes' : 'No' }}</p>
        </ion-label>
      </ion-item>
    </ion-card-content>
  </ion-card>

  <!-- Loading Indicator -->
  <ion-card *ngIf="isLoading">
    <ion-card-content>
      <ion-item lines="none">
        <ion-spinner slot="start"></ion-spinner>
        <ion-label>Running communication tests...</ion-label>
      </ion-item>
    </ion-card-content>
  </ion-card>

</ion-content>
