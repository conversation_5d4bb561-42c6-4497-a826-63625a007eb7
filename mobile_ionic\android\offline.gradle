// Enable offline mode to use cached dependencies
allprojects {
    repositories {
        mavenLocal()
    }
}

// Increase timeouts for all connections
gradle.projectsLoaded {
    rootProject.allprojects {
        buildscript {
            configurations.all {
                it.resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
                it.resolutionStrategy.cacheDynamicVersionsFor 0, 'seconds'
            }
        }
        configurations.all {
            it.resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
            it.resolutionStrategy.cacheDynamicVersionsFor 0, 'seconds'
        }
    }
}
