<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\Notification;
use Illuminate\Foundation\Testing\RefreshDatabase;

class NotificationTest extends TestCase
{
    use RefreshDatabase;

    public function test_notification_can_be_created()
    {
        $notification = Notification::create([
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ]);

        $this->assertInstanceOf(Notification::class, $notification);
        $this->assertEquals('Test Notification', $notification->title);
        $this->assertEquals('This is a test notification', $notification->message);
        $this->assertEquals('normal', $notification->severity);
        $this->assertEquals('test', $notification->category);
        $this->assertFalse($notification->sent);
    }

    public function test_notification_requires_title()
    {
        $this->expectException(\Illuminate\Database\QueryException::class);

        Notification::create([
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ]);
    }

    public function test_notification_requires_message()
    {
        $this->expectException(\Illuminate\Database\QueryException::class);

        Notification::create([
            'title' => 'Test Notification',
            'severity' => 'normal',
            'category' => 'test'
        ]);
    }

    public function test_notification_requires_severity()
    {
        $this->expectException(\Illuminate\Database\QueryException::class);

        Notification::create([
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'category' => 'test'
        ]);
    }

    public function test_notification_requires_category()
    {
        $this->expectException(\Illuminate\Database\QueryException::class);

        Notification::create([
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal'
        ]);
    }

    public function test_notification_severity_must_be_valid()
    {
        $this->expectException(\Illuminate\Database\QueryException::class);

        Notification::create([
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'invalid',
            'category' => 'test'
        ]);
    }

    public function test_notification_can_be_marked_as_sent()
    {
        $notification = Notification::create([
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ]);

        $notification->markAsSent();

        $this->assertTrue($notification->fresh()->sent);
    }

    public function test_notification_can_be_marked_as_failed()
    {
        $notification = Notification::create([
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ]);

        $notification->markAsFailed();

        $this->assertFalse($notification->fresh()->sent);
    }

    public function test_notification_can_be_queried_by_severity()
    {
        // Create notifications with different severities
        Notification::create([
            'title' => 'Normal Notification',
            'message' => 'This is a normal notification',
            'severity' => 'normal',
            'category' => 'test'
        ]);

        Notification::create([
            'title' => 'High Notification',
            'message' => 'This is a high severity notification',
            'severity' => 'high',
            'category' => 'test'
        ]);

        $normalNotifications = Notification::bySeverity('normal')->get();
        $highNotifications = Notification::bySeverity('high')->get();

        $this->assertCount(1, $normalNotifications);
        $this->assertEquals('Normal Notification', $normalNotifications->first()->title);

        $this->assertCount(1, $highNotifications);
        $this->assertEquals('High Notification', $highNotifications->first()->title);
    }

    public function test_notification_can_be_queried_by_category()
    {
        // Create notifications with different categories
        Notification::create([
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ]);

        Notification::create([
            'title' => 'Alert Notification',
            'message' => 'This is an alert notification',
            'severity' => 'normal',
            'category' => 'alert'
        ]);

        $testNotifications = Notification::byCategory('test')->get();
        $alertNotifications = Notification::byCategory('alert')->get();

        $this->assertCount(1, $testNotifications);
        $this->assertEquals('Test Notification', $testNotifications->first()->title);

        $this->assertCount(1, $alertNotifications);
        $this->assertEquals('Alert Notification', $alertNotifications->first()->title);
    }

    public function test_notification_can_be_queried_by_sent_status()
    {
        // Create sent and unsent notifications
        Notification::create([
            'title' => 'Sent Notification',
            'message' => 'This is a sent notification',
            'severity' => 'normal',
            'category' => 'test',
            'sent' => true
        ]);

        Notification::create([
            'title' => 'Unsent Notification',
            'message' => 'This is an unsent notification',
            'severity' => 'normal',
            'category' => 'test',
            'sent' => false
        ]);

        $sentNotifications = Notification::sent()->get();
        $unsentNotifications = Notification::unsent()->get();

        $this->assertCount(1, $sentNotifications);
        $this->assertEquals('Sent Notification', $sentNotifications->first()->title);

        $this->assertCount(1, $unsentNotifications);
        $this->assertEquals('Unsent Notification', $unsentNotifications->first()->title);
    }
} 